#!/usr/bin/env python3
"""
Script principal para ejecutar el bot de Guatemala Digital
Permite elegir entre modo prueba (inmediato) y modo monitoreo (con espera)
"""

from bot import GuatemalaDigitalBot, main, main_monitor

def show_menu():
    """Muestra el menú de opciones"""
    print("=" * 50)
    print("🤖 BOT GUATEMALA DIGITAL")
    print("=" * 50)
    print("1. Modo Prueba - Procesar productos inmediatamente")
    print("2. Modo Monitoreo - Esperar hasta que productos estén disponibles")
    print("3. Salir")
    print("=" * 50)

def main_menu():
    """Función principal con menú interactivo"""
    while True:
        show_menu()
        
        try:
            choice = input("Selecciona una opción (1-3): ").strip()
            
            if choice == "1":
                print("\n🚀 Iniciando modo PRUEBA...")
                print("📝 Leyendo URLs desde urls.txt...")
                main()  # Modo inmediato
                break
                
            elif choice == "2":
                print("\n⏰ Iniciando modo MONITOREO...")
                print("📝 Leyendo URLs desde urls.txt...")
                main_monitor()  # Modo con espera
                break
                
            elif choice == "3":
                print("👋 ¡Hasta luego!")
                break
                
            else:
                print("❌ Opción inválida. Por favor selecciona 1, 2 o 3.")
                input("Presiona Enter para continuar...")
                
        except KeyboardInterrupt:
            print("\n👋 ¡Hasta luego!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            input("Presiona Enter para continuar...")

if __name__ == "__main__":
    main_menu()
