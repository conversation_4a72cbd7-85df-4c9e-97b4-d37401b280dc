
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from webdriver_manager.firefox import GeckoDriverManager
import time
import datetime
import os
import winreg

class GuatemalaDigitalBot:
    def __init__(self):
        self.driver = None
        self.setup_driver()

    def get_default_browser(self):
        """Detecta el navegador predeterminado del sistema"""
        try:
            # Leer el registro de Windows para obtener el navegador predeterminado
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice") as key:
                prog_id = winreg.QueryValueEx(key, "ProgId")[0]

            if "Chrome" in prog_id:
                return "chrome"
            elif "Edge" in prog_id or "MSEdge" in prog_id:
                return "edge"
            elif "Firefox" in prog_id:
                return "firefox"
            else:
                print(f"Navegador no reconocido: {prog_id}, usando Edge por defecto")
                return "edge"
        except Exception as e:
            print(f"No se pudo detectar navegador predeterminado: {e}, usando Edge")
            return "edge"

    def setup_driver(self):
        """Configura el navegador predeterminado del sistema"""
        browser = self.get_default_browser()
        print(f"🌐 Usando navegador: {browser.title()}")

        try:
            if browser == "chrome":
                options = ChromeOptions()
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                # Usar perfil predeterminado
                user_data_dir = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
                if os.path.exists(user_data_dir):
                    options.add_argument(f"--user-data-dir={user_data_dir}")
                    options.add_argument("--profile-directory=Default")

                service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)

            elif browser == "firefox":
                options = FirefoxOptions()
                # Firefox no tiene las mismas opciones anti-detección
                service = FirefoxService(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)

            else:  # edge por defecto
                options = EdgeOptions()
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                # Usar perfil predeterminado
                user_data_dir = f"C:/Users/<USER>/AppData/Local/Microsoft/Edge/User Data"
                if os.path.exists(user_data_dir):
                    options.add_argument(f"--user-data-dir={user_data_dir}")
                    options.add_argument("--profile-directory=Default")

                service = EdgeService(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=service, options=options)

            # Ocultar que es un bot (para Chrome y Edge)
            if browser in ["chrome", "edge"]:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        except Exception as e:
            print(f"Error configurando {browser}: {e}")
            print("Intentando con Edge como respaldo...")
            self._setup_edge_fallback()

    def _setup_edge_fallback(self):
        """Configuración de respaldo usando Edge"""
        options = EdgeOptions()
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        service = EdgeService(EdgeChromiumDriverManager().install())
        self.driver = webdriver.Edge(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    def load_urls_from_file(self, filename="urls.txt"):
        """Carga URLs desde un archivo de texto"""
        try:
            if not os.path.exists(filename):
                print(f"❌ Archivo {filename} no encontrado")
                return []

            with open(filename, 'r', encoding='utf-8') as file:
                urls = [line.strip() for line in file.readlines() if line.strip()]

            print(f"📄 Cargadas {len(urls)} URLs desde {filename}")
            return urls

        except Exception as e:
            print(f"❌ Error leyendo archivo {filename}: {e}")
            return []

    def monitor_products(self, product_urls, check_interval=30):
        """
        Monitorea una lista de productos con temporizador

        Args:
            product_urls: Lista de URLs de productos
            check_interval: Segundos entre cada verificación
        """
        print(f"🚀 Iniciando monitoreo de {len(product_urls)} productos...")
        print(f"⏰ Verificando cada {check_interval} segundos")

        while product_urls:
            for url in product_urls.copy():
                try:
                    print(f"\n🔍 Verificando: {url}")
                    self.driver.get(url)
                    time.sleep(3)  # Esperar a que cargue la página

                    # Verificar si el producto ya está disponible
                    if self.is_product_available():
                        print("✅ ¡Producto disponible! Añadiendo al carrito...")
                        if self.add_to_cart():
                            print("🛒 ¡Producto añadido al carrito exitosamente!")
                            product_urls.remove(url)
                        else:
                            print("❌ Error al añadir al carrito")
                    else:
                        # Obtener tiempo restante del temporizador
                        remaining_time = self.get_timer_remaining()
                        if remaining_time:
                            print(f"⏳ Tiempo restante: {remaining_time}")
                        else:
                            print("⏳ Producto aún no disponible")

                except Exception as e:
                    print(f"❌ Error verificando producto: {e}")

                time.sleep(2)  # Pausa entre productos

            if product_urls:
                print(f"\n😴 Esperando {check_interval} segundos antes de la próxima verificación...")
                time.sleep(check_interval)

        print("\n🎉 ¡Todos los productos han sido procesados!")
        self.go_to_cart()

    def process_products_immediately(self, product_urls):
        """
        Procesa productos inmediatamente sin esperar (modo prueba)

        Args:
            product_urls: Lista de URLs de productos
        """
        print(f"🚀 Procesando {len(product_urls)} productos inmediatamente...")

        successful_additions = 0

        for i, url in enumerate(product_urls, 1):
            try:
                print(f"\n🔍 [{i}/{len(product_urls)}] Procesando: {url}")
                self.driver.get(url)
                time.sleep(3)  # Esperar a que cargue la página

                # Verificar si el producto está disponible
                if self.is_product_available():
                    print("✅ Producto disponible! Añadiendo al carrito...")
                    if self.add_to_cart():
                        print("🛒 ¡Producto añadido al carrito exitosamente!")
                        successful_additions += 1
                    else:
                        print("❌ Error al añadir al carrito")
                else:
                    # Verificar si tiene temporizador
                    remaining_time = self.get_timer_remaining()
                    if remaining_time:
                        print(f"⏳ Producto con temporizador - Tiempo restante: {remaining_time}")
                        print("⏭️ Saltando producto (no disponible aún)")
                    else:
                        print("❌ Producto no disponible o agotado - Saltando")

            except Exception as e:
                print(f"❌ Error procesando producto: {e}")

            # Pausa entre productos para no sobrecargar el servidor
            if i < len(product_urls):
                time.sleep(2)

        print(f"\n📊 Resumen: {successful_additions}/{len(product_urls)} productos añadidos al carrito")

        if successful_additions > 0:
            print("🛒 Abriendo carrito...")
            self.go_to_cart()
        else:
            print("ℹ️ No se añadieron productos al carrito")

    def is_product_available(self):
        """Verifica si el producto está disponible para compra"""
        try:
            # Verificar si hay temporizador de espera (producto no disponible aún)
            timer_selectors = [
                ".esperarOferta",
                "div.esperarOferta"
            ]

            for selector in timer_selectors:
                try:
                    timer_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if timer_element.is_displayed():
                        print("⏳ Producto con temporizador de espera encontrado")
                        return False
                except:
                    continue

            # Buscar botón específico "Añadir al carrito" con el ID exacto
            try:
                add_button = self.driver.find_element(By.ID, "add_to_cart")
                if add_button.is_enabled() and add_button.is_displayed():
                    # Verificar que el texto del botón sea correcto
                    button_text = add_button.text.lower()
                    if "añadir" in button_text and "carrito" in button_text:
                        return True
                    else:
                        print(f"Botón encontrado pero texto incorrecto: {button_text}")
                        return False
            except:
                print("❌ Botón 'Añadir al carrito' no encontrado")
                return False

            return False

        except Exception as e:
            print(f"Error verificando disponibilidad: {e}")
            return False

    def get_timer_remaining(self):
        """Obtiene el tiempo restante del temporizador específico"""
        try:
            # Buscar el div específico de espera de oferta
            timer_element = self.driver.find_element(By.CSS_SELECTOR, ".esperarOferta")
            if timer_element.is_displayed():
                # Buscar el div con el tiempo dentro del elemento
                time_div = timer_element.find_element(By.TAG_NAME, "div")
                time_text = time_div.text.strip()
                return time_text
            return None

        except Exception as e:
            return None

    def add_to_cart(self):
        """Añade el producto al carrito usando el selector específico"""
        try:
            # Usar el ID específico del botón que me proporcionaste
            add_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "add_to_cart"))
            )

            # Verificar que el botón tenga el texto correcto
            button_text = add_button.text.lower()
            if "añadir" in button_text and "carrito" in button_text:
                add_button.click()
                print("✅ Clic en 'Añadir al carrito' realizado")
                time.sleep(3)  # Esperar confirmación
                return True
            else:
                print(f"❌ Botón encontrado pero texto incorrecto: {add_button.text}")
                return False

        except Exception as e:
            print(f"❌ Error añadiendo al carrito: {e}")
            return False

    def go_to_cart(self):
        """Navega al carrito de compras usando el botón específico"""
        try:
            print("\n🛒 Navegando al carrito de compras...")

            # Usar el ID específico del botón carrito que me proporcionaste
            cart_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.ID, "carrito"))
            )

            cart_button.click()
            print("✅ ¡Carrito abierto!")
            time.sleep(3)  # Esperar a que cargue el carrito
            return True

        except Exception as e:
            print(f"❌ Error navegando al carrito: {e}")
            # Intentar ir directamente por URL como respaldo
            try:
                cart_url = "https://guatemaladigital.com/carrito"
                self.driver.get(cart_url)
                print("✅ Navegando directamente al carrito por URL")
                return True
            except:
                return False

    def close(self):
        """Cierra el navegador"""
        if self.driver:
            self.driver.quit()

# Función principal
def main():
    bot = GuatemalaDigitalBot()

    try:
        # Cargar URLs desde archivo
        products_to_monitor = bot.load_urls_from_file("urls.txt")

        if not products_to_monitor:
            print("❌ No se encontraron URLs para procesar")
            return

        print(f"🎯 Procesando {len(products_to_monitor)} productos...")

        # Procesar productos inmediatamente (modo prueba)
        bot.process_products_immediately(products_to_monitor)

        # Mantener el navegador abierto por un momento
        input("\nPresiona Enter para cerrar el navegador...")

    except KeyboardInterrupt:
        print("\n🛑 Proceso interrumpido por el usuario")
    except Exception as e:
        print(f"❌ Error general: {e}")
    finally:
        bot.close()

def main_monitor():
    """Función para monitoreo continuo (modo espera)"""
    bot = GuatemalaDigitalBot()

    try:
        # Cargar URLs desde archivo
        products_to_monitor = bot.load_urls_from_file("urls.txt")

        if not products_to_monitor:
            print("❌ No se encontraron URLs para procesar")
            return

        # Monitorear productos cada 30 segundos
        bot.monitor_products(products_to_monitor, check_interval=30)

        # Mantener el navegador abierto por un momento
        input("\nPresiona Enter para cerrar el navegador...")

    except KeyboardInterrupt:
        print("\n🛑 Monitoreo interrumpido por el usuario")
    except Exception as e:
        print(f"❌ Error general: {e}")
    finally:
        bot.close()

if __name__ == "__main__":
    main()
