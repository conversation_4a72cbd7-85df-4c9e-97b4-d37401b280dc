
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.microsoft import EdgeChromiumDriverManager
import time
import datetime

class GuatemalaDigitalBot:
    def __init__(self):
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        """Configura Edge con tu perfil existente"""
        edge_options = Options()

        # Usar tu perfil de Edge existente (ajusta la ruta según tu sistema)
        # Windows: C:/Users/<USER>/AppData/Local/Microsoft/Edge/User Data
        # edge_options.add_argument("--user-data-dir=C:/Users/<USER>/AppData/Local/Microsoft/Edge/User Data")
        # edge_options.add_argument("--profile-directory=Default")

        # Por ahora usaremos una sesión nueva, pero puedes descomentar las líneas de arriba
        edge_options.add_argument("--disable-blink-features=AutomationControlled")
        edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        edge_options.add_experimental_option('useAutomationExtension', False)

        service = Service(EdgeChromiumDriverManager().install())
        self.driver = webdriver.Edge(service=service, options=edge_options)

        # Ocultar que es un bot
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    def monitor_products(self, product_urls, check_interval=30):
        """
        Monitorea una lista de productos con temporizador

        Args:
            product_urls: Lista de URLs de productos
            check_interval: Segundos entre cada verificación
        """
        print(f"🚀 Iniciando monitoreo de {len(product_urls)} productos...")
        print(f"⏰ Verificando cada {check_interval} segundos")

        while product_urls:
            for url in product_urls.copy():
                try:
                    print(f"\n🔍 Verificando: {url}")
                    self.driver.get(url)
                    time.sleep(3)  # Esperar a que cargue la página

                    # Verificar si el producto ya está disponible
                    if self.is_product_available():
                        print("✅ ¡Producto disponible! Añadiendo al carrito...")
                        if self.add_to_cart():
                            print("🛒 ¡Producto añadido al carrito exitosamente!")
                            product_urls.remove(url)
                        else:
                            print("❌ Error al añadir al carrito")
                    else:
                        # Obtener tiempo restante del temporizador
                        remaining_time = self.get_timer_remaining()
                        if remaining_time:
                            print(f"⏳ Tiempo restante: {remaining_time}")
                        else:
                            print("⏳ Producto aún no disponible")

                except Exception as e:
                    print(f"❌ Error verificando producto: {e}")

                time.sleep(2)  # Pausa entre productos

            if product_urls:
                print(f"\n😴 Esperando {check_interval} segundos antes de la próxima verificación...")
                time.sleep(check_interval)

        print("\n🎉 ¡Todos los productos han sido procesados!")
        self.go_to_cart()

    def is_product_available(self):
        """Verifica si el producto está disponible para compra"""
        try:
            # Buscar botón "Añadir al carrito" - necesitaremos ajustar estos selectores
            add_to_cart_selectors = [
                "button[contains(text(), 'Añadir al carrito')]",
                "button[contains(text(), 'AÑADIR AL CARRITO')]",
                ".add-to-cart-btn",
                "#add-to-cart",
                "input[value*='carrito']",
                "button.btn-add-cart"
            ]

            for selector in add_to_cart_selectors:
                try:
                    if selector.startswith("button[contains"):
                        # XPath selector
                        element = self.driver.find_element(By.XPATH, f"//{selector}")
                    else:
                        # CSS selector
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element.is_enabled() and element.is_displayed():
                        return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"Error verificando disponibilidad: {e}")
            return False

    def get_timer_remaining(self):
        """Obtiene el tiempo restante del temporizador"""
        try:
            # Selectores comunes para temporizadores
            timer_selectors = [
                ".countdown",
                ".timer",
                "#countdown",
                ".time-remaining",
                "[class*='countdown']",
                "[id*='timer']"
            ]

            for selector in timer_selectors:
                try:
                    timer_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    return timer_element.text.strip()
                except:
                    continue

            return None

        except Exception as e:
            return None

    def add_to_cart(self):
        """Añade el producto al carrito"""
        try:
            # Intentar diferentes selectores para el botón de añadir al carrito
            add_to_cart_selectors = [
                "button[contains(text(), 'Añadir al carrito')]",
                "button[contains(text(), 'AÑADIR AL CARRITO')]",
                ".add-to-cart-btn",
                "#add-to-cart",
                "input[value*='carrito']",
                "button.btn-add-cart"
            ]

            for selector in add_to_cart_selectors:
                try:
                    if selector.startswith("button[contains"):
                        # XPath selector
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, f"//{selector}"))
                        )
                    else:
                        # CSS selector
                        element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )

                    element.click()
                    time.sleep(2)  # Esperar confirmación
                    return True

                except:
                    continue

            print("❌ No se encontró el botón 'Añadir al carrito'")
            return False

        except Exception as e:
            print(f"Error añadiendo al carrito: {e}")
            return False

    def go_to_cart(self):
        """Navega al carrito de compras"""
        try:
            print("\n🛒 Navegando al carrito de compras...")

            # Selectores comunes para el carrito
            cart_selectors = [
                "a[href*='carrito']",
                "a[href*='cart']",
                ".cart-link",
                "#cart-link",
                "a[contains(text(), 'Carrito')]",
                ".shopping-cart"
            ]

            for selector in cart_selectors:
                try:
                    if selector.startswith("a[contains"):
                        # XPath selector
                        cart_element = self.driver.find_element(By.XPATH, f"//{selector}")
                    else:
                        # CSS selector
                        cart_element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    cart_element.click()
                    print("✅ ¡Carrito abierto!")
                    return True

                except:
                    continue

            # Si no encuentra el enlace, intentar ir directamente
            cart_url = "https://guatemaladigital.com/carrito"  # URL típica del carrito
            self.driver.get(cart_url)
            print("✅ Navegando directamente al carrito")
            return True

        except Exception as e:
            print(f"Error navegando al carrito: {e}")
            return False

    def close(self):
        """Cierra el navegador"""
        if self.driver:
            self.driver.quit()

# Función principal
def main():
    # Lista de productos a monitorear
    products_to_monitor = [
        "https://guatemaladigital.com/Microphone-Replacement-for-Kingston-HyperX-Cloud-FlightFlight-S-Wireless-Gaming-Headset-Detachable-Mic-Boom-with-LED-Mute-Indicator-on-PC-PS5-PS4-Xbox-Series-XS-(Color:Black)/Producto/15080584",
        # Añade más URLs aquí
    ]

    bot = GuatemalaDigitalBot()

    try:
        # Monitorear productos cada 30 segundos
        bot.monitor_products(products_to_monitor, check_interval=30)

        # Mantener el navegador abierto por un momento
        input("\nPresiona Enter para cerrar el navegador...")

    except KeyboardInterrupt:
        print("\n🛑 Monitoreo interrumpido por el usuario")
    except Exception as e:
        print(f"❌ Error general: {e}")
    finally:
        bot.close()

if __name__ == "__main__":
    main()
