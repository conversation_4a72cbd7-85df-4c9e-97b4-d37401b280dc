from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import winreg

class GuatemalaDigitalBotWithProfile:
    def __init__(self):
        self.driver = None
        self.setup_driver()

    def get_default_browser(self):
        """Detecta el navegador predeterminado del sistema"""
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice") as key:
                prog_id = winreg.QueryValueEx(key, "ProgId")[0]
                
            if "Chrome" in prog_id:
                return "chrome"
            elif "Edge" in prog_id or "MSEdge" in prog_id:
                return "edge"
            elif "Firefox" in prog_id:
                return "firefox"
            else:
                return "edge"
        except:
            return "edge"

    def setup_driver(self):
        """Configura navegador con perfil persistente dedicado para el bot"""
        browser = self.get_default_browser()
        print(f"🌐 Usando navegador: {browser.title()} con perfil bot persistente")
        
        # Crear directorio para perfil del bot
        bot_profile_dir = os.path.join(os.getcwd(), "bot_profile")
        os.makedirs(bot_profile_dir, exist_ok=True)
        
        try:
            if browser == "chrome":
                options = ChromeOptions()
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                options.add_argument(f"--user-data-dir={bot_profile_dir}")
                options.add_argument("--profile-directory=BotProfile")
                
                self.driver = webdriver.Chrome(options=options)
                
            elif browser == "firefox":
                options = FirefoxOptions()
                # Firefox maneja perfiles diferente
                self.driver = webdriver.Firefox(options=options)
                
            else:  # edge
                options = EdgeOptions()
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                options.add_argument(f"--user-data-dir={bot_profile_dir}")
                options.add_argument("--profile-directory=BotProfile")
                
                self.driver = webdriver.Edge(options=options)

            # Ocultar que es un bot
            if browser in ["chrome", "edge"]:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                
            print("✅ Navegador configurado con perfil persistente del bot")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            raise

    def load_urls_from_file(self, filename="urls.txt"):
        """Carga URLs desde un archivo de texto"""
        try:
            if not os.path.exists(filename):
                print(f"❌ Archivo {filename} no encontrado")
                return []
            
            with open(filename, 'r', encoding='utf-8') as file:
                urls = [line.strip() for line in file.readlines() if line.strip()]
            
            print(f"📄 Cargadas {len(urls)} URLs desde {filename}")
            return urls
            
        except Exception as e:
            print(f"❌ Error leyendo archivo {filename}: {e}")
            return []

    def process_products_immediately(self, product_urls):
        """Procesa productos inmediatamente (modo prueba optimizado)"""
        print(f"🚀 Procesando {len(product_urls)} productos inmediatamente...")
        
        successful_additions = 0
        
        for i, url in enumerate(product_urls, 1):
            try:
                print(f"\n🔍 [{i}/{len(product_urls)}] Procesando: {url}")
                self.driver.get(url)
                
                # Esperar solo hasta que la página esté lista (más rápido)
                WebDriverWait(self.driver, 8).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )

                # Verificar disponibilidad rápidamente
                if self.is_product_available():
                    print("✅ Producto disponible! Añadiendo al carrito...")
                    if self.add_to_cart():
                        print("🛒 ¡Producto añadido al carrito exitosamente!")
                        successful_additions += 1
                    else:
                        print("❌ Error al añadir al carrito")
                else:
                    remaining_time = self.get_timer_remaining()
                    if remaining_time:
                        print(f"⏳ Producto con temporizador - Saltando")
                    else:
                        print("❌ Producto no disponible - Saltando")

            except Exception as e:
                print(f"❌ Error procesando producto: {e}")

            # Pausa mínima entre productos
            if i < len(product_urls):
                time.sleep(0.5)

        print(f"\n📊 Resumen: {successful_additions}/{len(product_urls)} productos añadidos al carrito")
        
        if successful_additions > 0:
            print("🛒 Abriendo carrito...")
            self.go_to_cart()
        else:
            print("ℹ️ No se añadieron productos al carrito")

    def is_product_available(self):
        """Verifica si el producto está disponible (optimizado)"""
        try:
            # Verificar temporizador primero (más rápido)
            try:
                timer_element = self.driver.find_element(By.CSS_SELECTOR, ".esperarOferta")
                if timer_element.is_displayed():
                    return False
            except:
                pass

            # Buscar botón de añadir al carrito
            try:
                add_button = self.driver.find_element(By.ID, "add_to_cart")
                if add_button.is_enabled() and add_button.is_displayed():
                    button_text = add_button.text.lower()
                    return "añadir" in button_text and "carrito" in button_text
            except:
                pass

            return False

        except Exception as e:
            return False

    def get_timer_remaining(self):
        """Obtiene el tiempo restante del temporizador (optimizado)"""
        try:
            timer_element = self.driver.find_element(By.CSS_SELECTOR, ".esperarOferta")
            if timer_element.is_displayed():
                time_div = timer_element.find_element(By.TAG_NAME, "div")
                return time_div.text.strip()
            return None
        except:
            return None

    def add_to_cart(self):
        """Añade el producto al carrito (optimizado)"""
        try:
            add_button = WebDriverWait(self.driver, 3).until(
                EC.element_to_be_clickable((By.ID, "add_to_cart"))
            )
            
            # Scroll y clic optimizado
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", add_button)
            time.sleep(0.3)
            
            try:
                add_button.click()
                print("✅ Clic realizado")
            except:
                self.driver.execute_script("arguments[0].click();", add_button)
                print("✅ Clic JS realizado")
            
            time.sleep(1)  # Confirmación rápida
            return True

        except Exception as e:
            print(f"❌ Error: {e}")
            return False

    def go_to_cart(self):
        """Navega al carrito (optimizado)"""
        try:
            print("\n🛒 Abriendo carrito...")
            cart_button = WebDriverWait(self.driver, 3).until(
                EC.element_to_be_clickable((By.ID, "carrito"))
            )
            cart_button.click()
            print("✅ ¡Carrito abierto!")
            time.sleep(1)
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            try:
                self.driver.get("https://guatemaladigital.com/carrito")
                print("✅ Carrito abierto por URL")
                return True
            except:
                return False

    def close(self):
        """Cierra el navegador"""
        if self.driver:
            self.driver.quit()

def main_fast():
    """Función principal optimizada"""
    bot = GuatemalaDigitalBotWithProfile()

    try:
        products_to_monitor = bot.load_urls_from_file("urls.txt")
        
        if not products_to_monitor:
            print("❌ No se encontraron URLs")
            return

        print(f"🎯 Procesando {len(products_to_monitor)} productos...")
        bot.process_products_immediately(products_to_monitor)
        input("\nPresiona Enter para cerrar...")

    except KeyboardInterrupt:
        print("\n🛑 Interrumpido")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        bot.close()

if __name__ == "__main__":
    main_fast()
