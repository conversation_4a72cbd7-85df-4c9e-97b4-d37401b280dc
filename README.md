# Bot Guatemala Digital - Automatización de Compras

Este bot automatiza la adición de productos al carrito en Guatemala Digital.

## 🚀 Características

- **Detección automática del navegador predeterminado** (Chrome, Edge, Firefox)
- **Lectura de URLs desde archivo** (`urls.txt`)
- **Dos modos de operación**:
  - **Modo <PERSON>**: Procesa productos inmediatamente
  - **Modo Monitoreo**: Espera hasta que productos estén disponibles
- **Manejo inteligente de productos**:
  - Salta productos con temporizador de espera
  - Salta productos agotados
  - Añade productos disponibles al carrito automáticamente

## 📋 Requisitos

```bash
pip install selenium webdriver-manager
```

## 📁 Archivos

- `bot.py` - Clase principal del bot
- `run_bot.py` - Script principal con menú interactivo
- `urls.txt` - Archivo con las URLs de productos (una por línea)
- `README.md` - Este archivo

## 🎯 Uso

### 1. Preparar URLs
Edita el archivo `urls.txt` y añade las URLs de los productos que quieres monitorear, una por línea:

```
https://guatemaladigital.com/producto1/Producto/123456
https://guatemaladigital.com/producto2/Producto/789012
```

### 2. Ejecutar el bot
```bash
python run_bot.py
```

### 3. Seleccionar modo
- **Opción 1 - Modo Prueba**: Procesa todos los productos inmediatamente, ideal para testing
- **Opción 2 - Modo Monitoreo**: Espera hasta que productos con temporizador estén disponibles

## 🔧 Funcionamiento

### Modo Prueba
1. Lee URLs desde `urls.txt`
2. Visita cada URL secuencialmente
3. Verifica si el producto está disponible
4. Si está disponible: lo añade al carrito
5. Si tiene temporizador o está agotado: lo salta
6. Al final abre el carrito con todos los productos añadidos

### Modo Monitoreo
1. Lee URLs desde `urls.txt`
2. Monitorea productos cada 30 segundos
3. Cuando un producto esté disponible: lo añade al carrito y lo remueve de la lista
4. Continúa hasta que todos los productos sean procesados
5. Al final abre el carrito

## 🎛️ Selectores Utilizados

El bot busca estos elementos específicos:

- **Botón añadir al carrito**: `#add_to_cart`
- **Temporizador de espera**: `.esperarOferta`
- **Botón del carrito**: `#carrito`

## ⚠️ Notas Importantes

- El bot usa tu perfil predeterminado del navegador cuando es posible
- Se incluyen medidas anti-detección básicas
- Pausa entre productos para no sobrecargar el servidor
- Maneja errores graciosamente y continúa con el siguiente producto
